<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BusinessCategory extends Model
{
    use SoftDeletes;

    protected $dates = ['deleted_at'];

    protected $fillable = [
        'parent_id',
        'name',
        'slug',
        'description',
        'status',
        'image'
    ];

     /**
     * Get subcategories for the business category.
     */
    public function subCategories()
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get the parent category.
     */
    public function parentCategory()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }
}
