<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Helpers\ImageUploadHelper;
use App\Http\Requests\CompanyProfileRequest;
use App\Http\Resources\CompanyProfileResource;
use App\Models\CompanyProfile;
use Faker\Provider\ar_EG\Company;
use Illuminate\Http\Request;

class CompanyProfileController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(CompanyProfileRequest $request)
    {
        try {

            $vendor = auth('vendor-api')->user();

            $validatedData = $request->validated();

            if ($request->hasFile('logo')) {
                $logoPhotoFile = $request->file('logo');
                $logoPhotoName = pathinfo($logoPhotoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $logoPhotoPath = (new ImageUploadHelper())->uploadImage($logoPhotoFile, 'public/uploads/company-logos', $logoPhotoName);

                $validatedData['logo'] = $logoPhotoPath;
            }

            if ($request->hasFile('company_registration')) {
                $companyRegFile = $request->file('company_registration');
                $companyRegName = pathinfo($companyRegFile->getClientOriginalName(), PATHINFO_FILENAME);
                $companyRegPath = (new ImageUploadHelper())->uploadImage($companyRegFile, 'public/uploads/company-registration', $companyRegName);

                $validatedData['company_registration'] = $companyRegPath;
            }

            if ($request->hasFile('vat_pan')) {
                $vatPanFile = $request->file('vat_pan');
                $vatPanName = pathinfo($vatPanFile->getClientOriginalName(), PATHINFO_FILENAME);
                $vatPanPath = (new ImageUploadHelper())->uploadImage($vatPanFile, 'public/uploads/vat-pan', $vatPanName);

                $validatedData['vat_pan'] = $vatPanPath;
            }

            if ($request->hasFile('tax_clearance')) {
                $taxClearanceFile = $request->file('tax_clearance');
                $taxClearanceName = pathinfo($taxClearanceFile->getClientOriginalName(), PATHINFO_FILENAME);
                $taxClearancePath = (new ImageUploadHelper())->uploadImage($taxClearanceFile, 'public/uploads/tax-clearance', $taxClearanceName);

                $validatedData['tax_clearance'] = $taxClearancePath;
            }

            if ($request->hasFile('additional_information')) {
                $additionalInfoFile = $request->file('additional_information');
                $additionalInfoName = pathinfo($additionalInfoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $additionalInfoPath = (new ImageUploadHelper())->uploadImage($additionalInfoFile, 'public/uploads/additional-info', $additionalInfoName);

                $validatedData['additional_information'] = $additionalInfoPath;
            }

            if ($request->hasFile('audit_report')) {
                $auditReportFile = $request->file('audit_report');
                $auditReportName = pathinfo($auditReportFile->getClientOriginalName(), PATHINFO_FILENAME);
                $auditReportPath = (new ImageUploadHelper())->uploadImage($auditReportFile, 'public/uploads/audit-report', $auditReportName);

                $validatedData['audit_report'] = $auditReportPath;
            }

            if ($request->hasFile('signature')) {
                $signatureFile = $request->file('signature');
                $signatureName = pathinfo($signatureFile->getClientOriginalName(), PATHINFO_FILENAME);
                $signaturePath = (new ImageUploadHelper())->uploadImage($signatureFile, 'public/uploads/signature', $signatureName);

                $validatedData['signature'] = $signaturePath;
            }

            $profile = CompanyProfile::updateOrCreate(
                ['vendor_id' => $vendor->id],
                $validatedData
            );

            if (!$profile) {
                return response()->json([
                    'success' => false,
                    'status_code' => 500,
                    'message' => 'Failed to create company profile'
                ], 500);
            }

            // Update vendor's kyc status
            $vendor->update([
                'kyc_status' => 'under_review'
            ]);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Company profile updated successfully',
                'data' => new CompanyProfileResource($profile)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to update company profile'
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show()
    {
        try {
            $vendor = auth('vendor-api')->user();

            $profile = $vendor->companyProfile;

            if (!$profile) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Company profile not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Company profile fetched successfully',
                'data' => new CompanyProfileResource($profile)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to fetch company profile'
            ], 500);
        }
    }
}
