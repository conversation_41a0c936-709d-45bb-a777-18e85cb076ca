<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vendor_id')->constrained();
            $table->string('name');
            $table->string('logo')->nullable();
            $table->string('email');
            $table->string('company_representative');
            $table->string('phone');
            $table->string('province');
            $table->string('district');
            $table->string('municipality');
            $table->string('address');
            $table->string('business_type');
            $table->string('website')->nullable();
            $table->string('company_registration');
            $table->string('vat_pan');
            $table->string('tax_clearance');
            $table->string('additional_information')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_profiles');
    }
};
