<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;
use App\Traits\HasRolesAndPermissions;

class Admin extends Model
{
    use HasApiTokens, Notifiable, HasRolesAndPermissions;

    protected $fillable = [
        'name',
        'email',
        'email_verified_at',
        'status',
        'remember_token',
        'profile',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'status' => 'boolean',
    ];
}
