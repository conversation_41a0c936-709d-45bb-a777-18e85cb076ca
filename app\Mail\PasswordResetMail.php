<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PasswordResetMail extends Mailable
{
    use Queueable, SerializesModels;

    public $code; // Public property to pass the OTP code

    /**
     * Create a new message instance.
     *
     * @param  int  $code
     * @return void
     */
    public function __construct($code)
    {
        $this->code = $code; // Assign the OTP code
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.password-reset')
                    ->subject('Password Reset Request')
                    ->with([
                        'code' => $this->code, // Pass the OTP code to the view
                    ]);
    }


}
