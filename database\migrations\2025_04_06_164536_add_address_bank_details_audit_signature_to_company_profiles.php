<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_profiles', function (Blueprint $table) {
            $table->after('additional_information', function ($table) {
                $table->string('ward_no')->nullable();
                $table->string('street_address')->nullable();
                $table->string('tole')->nullable();
                $table->string('bank_name')->nullable();
                $table->string('bank_branch')->nullable();
                $table->string('bank_account_number')->nullable();
                $table->string('account_holder_name')->nullable();
                $table->string('audit_report')->nullable();
                $table->string('signature')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_profiles', function (Blueprint $table) {
            $table->dropColumn([
                'ward_no',
                'street_address',
                'tole',
                'bank_account_number',
                'bank_account_holder_name',
                'bank_name',
                'bank_branch',
                'audit_report',
                'signature'
            ]);
        });
    }
};
