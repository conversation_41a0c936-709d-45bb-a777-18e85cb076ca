<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BusinessCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $businessCategoryId = $this->route('business_category');

        return [
            'parent_id' => 'nullable|exists:business_categories,id',
            'name' => [
                'required',
                'string',
                'max:255',
                $businessCategoryId 
                    ? Rule::unique('business_categories')->ignore($businessCategoryId)
                    : 'unique:business_categories'
            ],
            'description' => 'nullable|string|max:500',
            'image' => 'nullable|file|mimes:jpg,jpeg,png,gif|max:2048'
        ];
    }
}
