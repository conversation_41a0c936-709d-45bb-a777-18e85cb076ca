<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\FAQRequest;
use App\Http\Resources\FAQResource;
use App\Models\FAQ;

class FAQController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $faqs = FAQ::where('status', true)
                ->latest()->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'FAQs fetched successfully',
            'data' => FAQResource::collection($faqs)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FAQRequest $request)
    {
        try {
            $validatedData = $request->validated();

            $faq = FAQ::create($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 201,
                'message' => 'FAQ created successfully',
                'data' => new FAQResource($faq)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to create FAQ',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $faq = FAQ::find($id);

        if (!$faq) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'FAQ not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'FAQ fetched successfully',
            'data' => new FAQResource($faq)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FAQRequest $request, string $id)
    {
        try {
            $faq = FAQ::find($id);

            if (!$faq) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'FAQ not found'
                ], 404);
            }

            $validatedData = $request->validated();
            $faq->update($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'FAQ updated successfully',
                'data' => new FAQResource($faq)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to update FAQ',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $faq = FAQ::find($id);

            if (!$faq) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'FAQ not found'
                ], 404);
            }

            $faq->delete();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'FAQ deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to delete FAQ',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
