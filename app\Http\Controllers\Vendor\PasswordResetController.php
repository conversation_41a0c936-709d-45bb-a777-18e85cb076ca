<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Models\Vendor;
use App\Mail\PasswordResetMail;

class PasswordResetController extends Controller
{
    // Create Password Reset Request
    public function createPasswordResetRequest(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email|exists:vendors,email', // Ensure this checks the vendors table
            ]);

            $code = mt_rand(1000, 9999);

            // Update or insert the token into the password_reset_tokens table
            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $request->email],
                [
                    'token' => $code,
                    'created_at' => Carbon::now()
                ]
            );

            // Send the password reset token via email
            Mail::to($request->email)->send(new PasswordResetMail($code));

            return response()->json([
                'status' => true,
                'status_code' => 200,
                'message' => 'Password reset token sent via email.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'status_code' => 500,
                'message' => 'Failed to send password reset token via email.'
            ], 500);
        }
    }

    // Reset Password
    public function resetPassword(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'token' => 'required',
                'password' => 'required|min:8|confirmed',
            ]);

            // Retrieve the password reset entry
            $passwordResetEntry = DB::table('password_reset_tokens')
                ->where('email', $request->email)
                ->first();

            $tokenLifetime = 30; // Token expiration time in minutes

            // Check if the token is valid and not expired
            if (!$passwordResetEntry || Carbon::parse($passwordResetEntry->created_at)->addMinutes($tokenLifetime)->isPast()) {
                return response()->json([
                    'status' => false,
                    'status_code' => 401,
                    'message' => 'Invalid or expired token.'
                ], 401);
            }

            if ($request->token != $passwordResetEntry->token) {
                return response()->json([
                    'status' => false,
                    'status_code' => 401,
                    'errors' => 'Invalid token.'
                ], 401);
            }

            // Update the vendor's password
            $vendor = Vendor::where('email', $request->email)->first();
            $vendor->password = Hash::make($request->password);
            $vendor->save();

            // Remove the password reset token from the database
            DB::table('password_reset_tokens')->where('email', $request->email)->delete();

            return response()->json([
                'status' => true,
                'status_code' => 200,
                'message' => 'Password has been successfully reset.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'status_code' => 500,
                'message' => 'Failed to reset password.'
            ], 500);
        }
    }
}
