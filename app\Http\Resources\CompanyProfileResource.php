<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);
        $data['logo'] = $this->logo != null ? url('storage/uploads/logo-photos') . '/' . $this->logo : null;
        $data['company_registration'] = $this->company_registration != null ? url('storage/uploads/company-registration') . '/' . $this->company_registration : null;
        $data['vat_pan'] = $this->vat_pan != null ? url('storage/uploads/vat-pan') . '/' . $this->vat_pan : null;
        $data['tax_clearance'] = $this->tax_clearance != null ? url('storage/uploads/tax-clearance') . '/' . $this->tax_clearance : null;
        $data['additional_information'] = $this->additional_information != null ? url('storage/uploads/additional-information') . '/' . $this->additional_information : null;
        $data['audit_report'] = $this->audit_report != null ? url('storage/uploads/audit-report') . '/' . $this->audit_report : null;
        $data['signature'] = $this->signature != null ? url('storage/uploads/signature') . '/' . $this->signature : null;

        $data['kyc_status'] = $this->vendor->kyc_status ?? null;

        return $data;
    }
}
