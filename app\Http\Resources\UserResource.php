<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'address' => $this->address,
            'phone' => $this->phone,
            'citizenship' => $this->citizenship != null ? url('storage/uploads/citizenship').'/'.$this->citizenship : null,
            'pan' => $this->pan != null ? url('storage/uploads/pan').'/'.$this->pan : null,
            'profile' => $this->profile != null ? url('storage/uploads/profile-photos').'/'.$this->profile : null,
            'status' => $this->status,
            'type' => $this->type,
            'kyc_status' => $this->kyc_status,
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'permissions' => $this->when($this->relationLoaded('roles'), function () {
                return $this->permissions()->pluck('name');
            })
        ];
    }
}
