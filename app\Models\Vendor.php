<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens; 

class Vendor extends Authenticatable
{
    use HasFactory, Notifiable, HasApiTokens, SoftDeletes;
    
    protected $fillable = [
        'name',
        'email',
        'profile',
        'password',
        'address',
        'status',
        'kyc_status'
    ];

    public function companyProfile()
    {
        return $this->hasOne(CompanyProfile::class);
    }
}
