<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\ImageUploadHelper;
use App\Http\Resources\VendorResource;
use App\Models\Vendor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class VendorController extends Controller
{
    // Get Vendor Profile
    public function profile(Request $request)
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'status_code' => 401,
                'message' => 'Unauthorized'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Profile fetched successfully',
            'data' => new VendorResource($request->user())
        ]);
    }

    // Logout Vendor
    public function logout(Request $request)
    {
        // Check if the vendor is authenticated
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'status_code' => 401,
                'message' => 'Unauthorized'
            ], 401);
        }

        // Revoke all tokens for the authenticated vendor
        $request->user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Logged out successfully'
        ]);
    }

    // Token Refresh
    public function refresh(Request $request)
    {
        $token = $request->user()->createToken('vendor-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Token refreshed successfully',
            'token' => $token
        ]);
    }

    // Change Password
    public function changePassword(Request $request)
    {
        try {
            $vendor = $request->user();

            $validatedData = $request->validate([
                'current_password' => 'required',
                'password' => 'required|string|min:8|confirmed',
            ]);

            if (!Hash::check($validatedData['current_password'], $vendor->password)) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'message' => 'Current password is incorrect'
                ], 422);
            }

            $vendor->password = Hash::make($validatedData['password']);
            $vendor->save();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Password changed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    // Reset Vendor Password
    public function resetVendorPassword(Request $request)
    {
        try {
            $request->validate([
                'vendor_id' => 'required|exists:vendors,id',
                'password' => 'required|min:8|confirmed',
            ]);

            // Find the vendor by ID
            $vendor = Vendor::find($request->vendor_id);

            if (!$vendor) {
                return response()->json([
                    'status' => false,
                    'status_code' => 404,
                    'message' => 'Vendor not found'
                ], 404);
            }

            // Update vendor's password
            $vendor->password = Hash::make($request->password);
            $vendor->save();

            return response()->json([
                'status' => true,
                'status_code' => 200,
                'message' => 'Password reset successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    // Update Vendor Profile
    public function updateProfile(Request $request)
    {
        try {
            $vendor = Auth::user();

            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:vendors,email,' . $vendor->id,
                'address' => 'nullable|string',
                'profile' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:5120',
            ]);

            $vendor->fill($validatedData);
            // Validate and handle profile photo upload
            if ($request->hasFile('profile')) {
                $profilePhotoFile = $request->file('profile');
                $profilePhotoName = pathinfo($profilePhotoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $profilePhotoPath = (new ImageUploadHelper())->uploadImage($profilePhotoFile, 'public/uploads/profile-photos', $profilePhotoName);

                $vendor->profile = $profilePhotoPath;
            }
            $vendor->save();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Profile updated successfully',
                'data' => new VendorResource($vendor)
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }
}
