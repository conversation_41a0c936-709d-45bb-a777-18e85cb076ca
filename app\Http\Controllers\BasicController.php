<?php

namespace App\Http\Controllers;

use App\Models\District;
use App\Models\Municipality;
use App\Models\Province;
use Illuminate\Http\Request;

class BasicController extends Controller
{
    public function provice()
    {
        $province = Province::all();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Provinces fetched successfully',
            'data' => $province
        ], 200);
    }

    public function getDistrictsByProvince($province_id)
    {
        $districts = District::where('province_id', $province_id)->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Districts fetched successfully',
            'data' => $districts
        ]);
    }

    public function getMunicipalitiesByDistrict($district_id)
    {
        $municiplities = Municipality::where('district_id', $district_id)->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Municipalities fetched successfully',
            'data' => $municiplities
        ]);
    }
}
