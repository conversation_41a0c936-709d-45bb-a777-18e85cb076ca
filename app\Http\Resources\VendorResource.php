<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'address' => $this->address,
            'profile' => $this->profile != null ? url('storage/uploads/profile-photos').'/'.$this->profile : null,
            'status' => $this->status,
            'kyc_status' => $this->kyc_status
        ];
    }
}
