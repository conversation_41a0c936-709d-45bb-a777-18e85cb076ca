<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class VendorTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('vendors')->insert([
            'name' => 'Demo Vendor',
            'email' => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'profile' => 'profile.png',
            'password' => Hash::make('vendor@123'),
            'status' => true,
            'kyc_status' => 'pending'
        ]);
    }
}
