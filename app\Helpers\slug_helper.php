<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

if (!function_exists('generateUniqueSlug')) {
    /**
     * Generate a unique slug for a given model and field, with special characters removed.
     *
     * @param string|Model $model The model class or instance (e.g., \App\Models\Category::class or new Category()).
     * @param string $title The title or name to create the slug from.
     * @param string $field The field that stores the slug (default is 'slug').
     * @param int|null $excludeId Optionally exclude a record by ID (useful for updates).
     * @return string The unique slug.
     */
    function generateUniqueSlug($model, $title, $field = 'slug', $excludeId = null)
    {
        // Remove special characters from the title before generating slug
        $cleanTitle = preg_replace('/[^A-Za-z0-9 ]/', '', $title);

        // Generate initial slug using only alphanumeric characters and hyphens
        $slug = Str::slug($cleanTitle);
        $originalSlug = $slug;
        $counter = 1;

        // Get the table name dynamically
        $table = $model instanceof Model ? $model->getTable() : (new $model)->getTable();

        // Prepare the base query
        $query = DB::table($table)->where($field, $slug);

        // Exclude the record by ID if provided (useful for updates)
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        // Check for an existing slug in the database and modify if needed
        while ($query->exists()) {
            $slug = $originalSlug . '-' . $counter++;
            $query = DB::table($table)->where($field, $slug);

            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
        }

        return $slug;
    }
}
