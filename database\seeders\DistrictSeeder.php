<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DistrictSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::unprepared("
            INSERT INTO `districts` (`id`, `name`, `province_id`) VALUES
            (1, 'Bhojpur', 1),
            (2, '<PERSON><PERSON><PERSON><PERSON>', 1),
            (3, 'Ilam', 1),
            (4, '<PERSON><PERSON><PERSON>', 1),
            (5, 'Khotang', 1),
            (6, '<PERSON><PERSON>', 1),
            (7, 'Ok<PERSON>dhung<PERSON>', 1),
            (8, '<PERSON><PERSON><PERSON>', 1),
            (9, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 1),
            (10, '<PERSON><PERSON><PERSON><PERSON>', 1),
            (11, '<PERSON><PERSON>', 1),
            (12, '<PERSON>ple<PERSON><PERSON>', 1),
            (13, '<PERSON><PERSON><PERSON><PERSON>', 1),
            (14, '<PERSON><PERSON><PERSON><PERSON>', 1),
            (15, '<PERSON><PERSON><PERSON>', 2),
            (16, '<PERSON><PERSON><PERSON>', 2),
            (17, '<PERSON><PERSON><PERSON><PERSON>', 2),
            (18, '<PERSON><PERSON><PERSON><PERSON>', 2),
            (19, '<PERSON><PERSON><PERSON>', 2),
            (20, '<PERSON><PERSON>', 2),
            (21, '<PERSON><PERSON>', 2),
            (22, '<PERSON><PERSON><PERSON>', 2),
            (23, '<PERSON><PERSON>', 3),
            (24, '<PERSON><PERSON><PERSON><PERSON>', 3),
            (25, '<PERSON><PERSON><PERSON>', 3),
            (26, '<PERSON><PERSON><PERSON><PERSON>', 3),
            (27, '<PERSON><PERSON><PERSON>', 3),
            (28, '<PERSON><PERSON><PERSON>', 3),
            (29, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 3),
            (30, '<PERSON><PERSON><PERSON>', 3),
            (31, '<PERSON><PERSON><PERSON><PERSON>', 3),
            (32, 'Rasuwa', 3),
            (33, 'Sindhupalchok', 3),
            (34, 'Chitwan', 3),
            (35, 'Makwanpur', 3),
            (36, 'Baglung', 4),
            (37, 'Gorkha', 4),
            (38, 'Kaski', 4),
            (39, 'Lamjung', 4),
            (40, 'Manang', 4),
            (41, 'Mustang', 4),
            (42, 'Myagdi', 4),
            (43, 'Nawalpur', 4),
            (44, 'Parbat', 4),
            (45, 'Syangja', 4),
            (46, 'Tanahun', 4),
            (47, 'Kapilvastu', 5),
            (48, 'Parasi', 5),
            (49, 'Rupandehi', 5),
            (50, 'Arghakhanchi', 5),
            (51, 'Gulmi', 5),
            (52, 'Palpa', 5),
            (53, 'Dang', 5),
            (54, 'Pyuthan', 5),
            (55, 'Rolpa', 5),
            (56, 'Eastern Rukum', 5),
            (57, 'Banke', 5),
            (58, 'Bardiya', 5),
            (59, 'Western Rukum', 6),
            (60, 'Salyan', 6),
            (61, 'Dolpa', 6),
            (62, 'Humla', 6),
            (63, 'Jumla', 6),
            (64, 'Kalikot', 6),
            (65, 'Mugu', 6),
            (66, 'Surkhet', 6),
            (67, 'Dailekh', 6),
            (68, 'Jajarkot', 6),
            (69, 'Kailali', 7),
            (70, 'Achham', 7),
            (71, 'Doti', 7),
            (72, 'Bajhang', 7),
            (73, 'Bajura', 7),
            (74, 'Kanchanpur', 7),
            (75, 'Dadeldhura', 7),
            (76, 'Baitadi', 7),
            (77, 'Darchula', 7);
        ");
    }
}
