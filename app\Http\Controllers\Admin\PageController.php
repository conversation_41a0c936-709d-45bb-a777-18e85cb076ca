<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ImageUploadHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\AboutUsRequest;
use App\Http\Requests\ContactUsRequest;
use App\Http\Resources\AboutUsResource;
use App\Http\Resources\ContactUsResource;
use App\Models\AboutUs;
use App\Models\ContactUs;

class PageController extends Controller
{
    // About Us
    public function getAboutUs()
    {
        $aboutUs = AboutUs::first();

        if (!$aboutUs) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'About Us not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'About Us fetched successfully',
            'data' => new AboutUsResource($aboutUs)
        ]);
    }

    public function aboutUs(AboutUsRequest $request)
    {
        $validatedData = $request->validated();

        if ($request->hasFile('image')) {
            $imageFile = $request->file('image');
            $imageName = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
            $imagePath = (new ImageUploadHelper())->uploadImage($imageFile, 'public/uploads/misc', $imageName);

            $validatedData['image'] = $imagePath;
        }

        // Use updateOrCreate for upsert functionality
        $aboutUs = AboutUs::updateOrCreate(
            [],
            $validatedData
        );

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'About Us saved successfully',
            'data' => new AboutUsResource($aboutUs)
        ]);
    }

    // Contact Us
    public function getContactUs()
    {
        $contactUs = ContactUs::first();

        if (!$contactUs) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'Contact Us not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Contact Us fetched successfully',
            'data' => new ContactUsResource($contactUs)
        ]);
    }

    public function contactUs(ContactUsRequest $request)
    {
        $validatedData = $request->validated();

        // Use updateOrCreate for upsert functionality
        $contactUs = ContactUs::updateOrCreate(
            [],
            $validatedData
        );

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Contact Us saved successfully',
            'data' => new ContactUsResource($contactUs)
        ]);
    }
}
