<?php

namespace App\Http\Controllers;

use App\Http\Resources\AboutUsResource;
use App\Http\Resources\ContactUsResource;
use App\Http\Resources\FAQResource;
use App\Http\Resources\NoticeResource;
use App\Http\Resources\TrainingSessionResource;
use App\Models\AboutUs;
use App\Models\ContactUs;
use App\Models\FAQ;
use App\Models\Notice;
use App\Models\TrainingSession;
use Illuminate\Http\Request;

class ClientSideController extends Controller
{
    // About Us
    public function getAboutUs()
    {
        $aboutUs = AboutUs::first();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'About Us fetched successfully',
            'data' => $aboutUs ? new AboutUsResource($aboutUs) : (object) []
        ]);
    }

    // Contact Us
    public function getContactUs()
    {
        $contactUs = ContactUs::first();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Contact Us fetched successfully',
            'data' => $contactUs ? new ContactUsResource($contactUs) : (object) []
        ]);
    }

    // Training Sessions
    public function trainingSession()
    {
        $trainingSessions = TrainingSession::latest()->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Training sessions fetched successfully',
            'data' => TrainingSessionResource::collection($trainingSessions)
        ]);
    }

    // FAQs
    public function faq()
    {
        $faqs = FAQ::where('status', true)
                ->latest()->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'FAQs fetched successfully',
            'data' => FAQResource::collection($faqs)
        ]);
    }

    // Notice
    public function notice()
    {
        $notices = Notice::where('status', true)
                ->latest()->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Notices fetched successfully',
            'data' => NoticeResource::collection($notices)
        ]);
    }
}
