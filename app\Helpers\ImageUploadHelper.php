<?php
namespace App\Helpers;

use Illuminate\Support\Facades\Storage;


class ImageUploadHelper{


    public function uploadImage($file, $foldername, $name = ''){

        if($file){            
            $directory = $foldername;
           //  if (!file_exists($directory)) {
           //     mkdir($directory, 0777, true);
           //  }
            $fileName = time().'-'.$name.'.'.$file->getClientOriginalExtension();
            $file->storeAs($directory, $fileName);
            return $fileName;

        }else{

            return '';

        }

    }

    public function deleteImage($filePath)
    {
        // Ensure that the file exists before attempting to delete
        // 'public/' is assumed to be the disk name, so we use 'public/' prefix here
        if (Storage::exists('public/' . $filePath)) {
            return Storage::delete('public/' . $filePath);
        }

        return false;
    }

}