<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyProfile extends Model
{
    protected $fillable = [
        'vendor_id',
        'name',
        'logo',
        'email',
        'company_representative',
        'phone',
        'province',
        'district',
        'municipality',
        'address',
        'business_type',
        'website',
        'company_registration',
        'vat_pan',
        'tax_clearance',
        'additional_information',
        'ward_no',
        'street_address',
        'tole',
        'bank_name',
        'bank_branch',
        'bank_account_number',
        'account_holder_name',
        'audit_report',
        'signature',
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
}
