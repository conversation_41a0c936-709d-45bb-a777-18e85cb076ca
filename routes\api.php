<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController as AdminAuthController;
use App\Http\Controllers\Customer\AuthController as CustomerAuthController;
use App\Http\Controllers\Vendor\AuthController as VendorAuthController;
use App\Http\Controllers\Admin\PasswordResetController as AdminPasswordResetController;
use App\Http\Controllers\Customer\PasswordResetController as CustomerPasswordResetController;
use App\Http\Controllers\Vendor\PasswordResetController as VendorPasswordResetController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\BusinessCategoryController;
use App\Http\Controllers\Admin\FAQController;
use App\Http\Controllers\Admin\NoticeController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserRoleController;
use App\Http\Controllers\Admin\TrainingSessionController;
use App\Http\Controllers\BasicController;
use App\Http\Controllers\ClientSideController;
use App\Http\Controllers\Customer\CustomerController;
use App\Http\Controllers\Vendor\CompanyProfileController;
use App\Http\Controllers\Vendor\VendorController;

Route::post('/admin/login', [AdminAuthController::class, 'login']);
Route::post('/admin/password-reset-request', [AdminPasswordResetController::class, 'createPasswordResetRequest']);
Route::put('/admin/reset-password', [AdminPasswordResetController::class, 'resetPassword']);

Route::post('/customer/login', [CustomerAuthController::class, 'login']);
Route::post('/customer/register', [CustomerAuthController::class, 'register']);
Route::post('/customer/password-reset-request', [CustomerPasswordResetController::class, 'createPasswordResetRequest']);
Route::put('/customer/reset-password', [CustomerPasswordResetController::class, 'resetPassword']);

Route::post('/vendor/login', [VendorAuthController::class, 'login']);
Route::post('/vendor/register', [VendorAuthController::class, 'register']);
Route::post('/vendor/password-reset-request', [VendorPasswordResetController::class, 'createPasswordResetRequest']);
Route::put('/vendor/reset-password', [VendorPasswordResetController::class, 'resetPassword']);

Route::get('/about-us', [ClientSideController::class, 'getAboutUs']);
Route::get('/contact-us', [ClientSideController::class, 'getContactUs']);
Route::get('/training-session', [ClientSideController::class, 'trainingSession']);
Route::get('/faq', [ClientSideController::class, 'faq']);
Route::get('/notice', [ClientSideController::class, 'notice']);

Route::get('/provinces', [BasicController::class, 'provice']);
Route::get('/districts-by-province/{province_id}', [BasicController::class, 'getDistrictsByProvince']);
Route::get('/municipalities-by-district/{district_id}', [BasicController::class, 'getMunicipalitiesByDistrict']);

Route::middleware('auth:admin-api')->prefix('admin')->group(function () {
    Route::get('/profile', [AdminController::class, 'profile']);
    Route::post('/logout', [AdminController::class, 'logout']);
    Route::post('/refresh', [AdminController::class, 'refresh']);
    Route::post('/change-password', [AdminController::class, 'changePassword']);
    Route::put('/profile', [AdminController::class, 'updateProfile']);
    Route::get('/dashboard', [AdminController::class, 'dashboard']);

    // Business Category
    Route::middleware(['permission:business_category.view'])->group(function () {
        Route::get('/business-category', [BusinessCategoryController::class, 'index']);
        Route::get('/business-category/{id}', [BusinessCategoryController::class, 'show']);
        Route::get('/business-category/{id}/subcategories', [BusinessCategoryController::class, 'getSubCategoriesByBusinessCategory']);
    });
    Route::middleware(['permission:business_category.create'])->post('/business-category', [BusinessCategoryController::class, 'store']);
    Route::middleware(['permission:business_category.edit'])->put('/business-category/{id}', [BusinessCategoryController::class, 'update']);
    Route::middleware(['permission:business_category.delete'])->delete('/business-category/{id}', [BusinessCategoryController::class, 'destroy']);

    // Training Sessions
    Route::middleware(['permission:training_session.view'])->group(function () {
        Route::get('/training-session', [TrainingSessionController::class, 'index']);
        Route::get('/training-session/{id}', [TrainingSessionController::class, 'show']);
    });
    Route::middleware(['permission:training_session.create'])->post('/training-session', [TrainingSessionController::class, 'store']);
    Route::middleware(['permission:training_session.edit'])->put('/training-session/{id}', [TrainingSessionController::class, 'update']);
    Route::middleware(['permission:training_session.delete'])->delete('/training-session/{id}', [TrainingSessionController::class, 'destroy']);

    // FAQs
    Route::middleware(['permission:faq.view'])->group(function () {
        Route::get('/faq', [FAQController::class, 'index']);
        Route::get('/faq/{id}', [FAQController::class, 'show']);
    });
    Route::middleware(['permission:faq.create'])->post('/faq', [FAQController::class, 'store']);
    Route::middleware(['permission:faq.edit'])->put('/faq/{id}', [FAQController::class, 'update']);
    Route::middleware(['permission:faq.delete'])->delete('/faq/{id}', [FAQController::class, 'destroy']);

    // Notices
    Route::middleware(['permission:notice.view'])->group(function () {
        Route::get('/notice', [NoticeController::class, 'index']);
        Route::get('/notice/{id}', [NoticeController::class, 'show']);
    });
    Route::middleware(['permission:notice.create'])->post('/notice', [NoticeController::class, 'store']);
    Route::middleware(['permission:notice.edit'])->put('/notice/{id}', [NoticeController::class, 'update']);
    Route::middleware(['permission:notice.delete'])->delete('/notice/{id}', [NoticeController::class, 'destroy']);

    // pages
    Route::middleware(['permission:page.view'])->group(function () {
        // About Us
        Route::get('/about-us', [PageController::class, 'getAboutUs']);
        // Contact us
        Route::get('/contact-us', [PageController::class, 'getContactUs']);
    });

    Route::middleware(['permission:page.edit'])->group(function () {
        // About Us
        Route::put('/about-us', [PageController::class, 'aboutUs']);
        // Contact us
        Route::put('/contact-us', [PageController::class, 'contactUs']);
    });

    // Roles and Permissions Management (Super Admin only)
    Route::middleware(['role:super_admin'])->group(function () {
        // Roles
        Route::apiResource('/roles', RoleController::class);

        // Permissions
        Route::apiResource('/permissions', PermissionController::class);
        Route::get('/permissions/grouped', [PermissionController::class, 'getGrouped']);

        // User Role Management
        Route::get('/users/{userType}', [UserRoleController::class, 'getUsersByType']);
        Route::get('/users/{userType}/{userId}/roles', [UserRoleController::class, 'getUserRoles']);
        Route::post('/users/{userType}/{userId}/roles', [UserRoleController::class, 'assignRoles']);
        Route::delete('/users/{userType}/{userId}/roles', [UserRoleController::class, 'removeRoles']);
    });

});

// Customer
Route::middleware('auth:sanctum')->prefix('customer')->group(function () {
    Route::get('/profile', [CustomerController::class, 'profile']);
    Route::post('/logout', [CustomerController::class, 'logout']);
    Route::post('/refresh', [CustomerController::class, 'refresh']);
    Route::post('/change-password', [CustomerController::class, 'changePassword']);
    Route::put('/profile', [CustomerController::class, 'updateProfile']);
    Route::put('/verify-kyc', [CustomerController::class, 'verifyKyc']);
    Route::get('/dashboard', [CustomerController::class, 'dashboard']);

    // Business Category
    Route::get('/business-category', [BusinessCategoryController::class, 'index']);
    Route::get('/business-category/{id}/subcategories', [BusinessCategoryController::class, 'getSubCategoriesByBusinessCategory']);
});

// Vendor
Route::middleware('auth:vendor-api')->prefix('vendor')->group(function () {
    Route::get('/profile', [VendorController::class, 'profile']);
    Route::post('/logout', [VendorController::class, 'logout']);
    Route::post('/refresh', [VendorController::class, 'refresh']);
    Route::post('/change-password', [VendorController::class, 'changePassword']);
    Route::put('/profile', [VendorController::class, 'updateProfile']);
    Route::get('/dashboard', [VendorController::class, 'dashboard']);

    Route::apiResource('/company-profile', CompanyProfileController::class)->only(['show', 'store']);
    Route::apiResource('/business-category', BusinessCategoryController::class)->only(['index']);
    Route::get('/business-category/{id}/subcategories', [BusinessCategoryController::class, 'getSubCategoriesByBusinessCategory']);
});
