<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController as AdminAuthController;
use App\Http\Controllers\Customer\AuthController as CustomerAuthController;
use App\Http\Controllers\Vendor\AuthController as VendorAuthController;
use App\Http\Controllers\Admin\PasswordResetController as AdminPasswordResetController;
use App\Http\Controllers\Customer\PasswordResetController as CustomerPasswordResetController;
use App\Http\Controllers\Vendor\PasswordResetController as VendorPasswordResetController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\BusinessCategoryController;
use App\Http\Controllers\Admin\FAQController;
use App\Http\Controllers\Admin\NoticeController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\TrainingSessionController;
use App\Http\Controllers\BasicController;
use App\Http\Controllers\ClientSideController;
use App\Http\Controllers\Customer\CustomerController;
use App\Http\Controllers\Vendor\CompanyProfileController;
use App\Http\Controllers\Vendor\VendorController;

Route::post('/admin/login', [AdminAuthController::class, 'login']);
Route::post('/admin/password-reset-request', [AdminPasswordResetController::class, 'createPasswordResetRequest']);
Route::put('/admin/reset-password', [AdminPasswordResetController::class, 'resetPassword']);

Route::post('/customer/login', [CustomerAuthController::class, 'login']);
Route::post('/customer/register', [CustomerAuthController::class, 'register']);
Route::post('/customer/password-reset-request', [CustomerPasswordResetController::class, 'createPasswordResetRequest']);
Route::put('/customer/reset-password', [CustomerPasswordResetController::class, 'resetPassword']);

Route::post('/vendor/login', [VendorAuthController::class, 'login']);
Route::post('/vendor/register', [VendorAuthController::class, 'register']);
Route::post('/vendor/password-reset-request', [VendorPasswordResetController::class, 'createPasswordResetRequest']);
Route::put('/vendor/reset-password', [VendorPasswordResetController::class, 'resetPassword']);

Route::get('/about-us', [ClientSideController::class, 'getAboutUs']);
Route::get('/contact-us', [ClientSideController::class, 'getContactUs']);
Route::get('/training-session', [ClientSideController::class, 'trainingSession']);
Route::get('/faq', [ClientSideController::class, 'faq']);
Route::get('/notice', [ClientSideController::class, 'notice']);

Route::get('/provinces', [BasicController::class, 'provice']);
Route::get('/districts-by-province/{province_id}', [BasicController::class, 'getDistrictsByProvince']);
Route::get('/municipalities-by-district/{district_id}', [BasicController::class, 'getMunicipalitiesByDistrict']);

Route::middleware('auth:admin-api')->prefix('admin')->group(function () {
    Route::get('/profile', [AdminController::class, 'profile']);
    Route::post('/logout', [AdminController::class, 'logout']);
    Route::post('/refresh', [AdminController::class, 'refresh']);
    Route::post('/change-password', [AdminController::class, 'changePassword']);
    Route::put('/profile', [AdminController::class, 'updateProfile']);
    Route::get('/dashboard', [AdminController::class, 'dashboard']);

    // Business Category
    Route::apiResource('/business-category', BusinessCategoryController::class);
    Route::get('/business-category', [BusinessCategoryController::class, 'index']);
    Route::get('/business-category/{id}/subcategories', [BusinessCategoryController::class, 'getSubCategoriesByBusinessCategory']);

    // Training Sessions
    Route::apiResource('/training-session', TrainingSessionController::class);

    // FAQs
    Route::apiResource('/faq', FAQController::class);

    // Notices
    Route::apiResource('/notice', NoticeController::class);

    // pages
    // About Us
    Route::get('/about-us', [PageController::class, 'getAboutUs']);
    Route::put('/about-us', [PageController::class, 'aboutUs']);

    // Contact us
    Route::get('/contact-us', [PageController::class, 'getContactUs']);
    Route::put('/contact-us', [PageController::class, 'contactUs']);

});

// Customer
Route::middleware('auth:sanctum')->prefix('customer')->group(function () {
    Route::get('/profile', [CustomerController::class, 'profile']);
    Route::post('/logout', [CustomerController::class, 'logout']);
    Route::post('/refresh', [CustomerController::class, 'refresh']);
    Route::post('/change-password', [CustomerController::class, 'changePassword']);
    Route::put('/profile', [CustomerController::class, 'updateProfile']);
    Route::put('/verify-kyc', [CustomerController::class, 'verifyKyc']);
    Route::get('/dashboard', [CustomerController::class, 'dashboard']);

    // Business Category
    Route::get('/business-category', [BusinessCategoryController::class, 'index']);
    Route::get('/business-category/{id}/subcategories', [BusinessCategoryController::class, 'getSubCategoriesByBusinessCategory']);
});

// Vendor
Route::middleware('auth:vendor-api')->prefix('vendor')->group(function () {
    Route::get('/profile', [VendorController::class, 'profile']);
    Route::post('/logout', [VendorController::class, 'logout']);
    Route::post('/refresh', [VendorController::class, 'refresh']);
    Route::post('/change-password', [VendorController::class, 'changePassword']);
    Route::put('/profile', [VendorController::class, 'updateProfile']);
    Route::get('/dashboard', [VendorController::class, 'dashboard']);

    Route::apiResource('/company-profile', CompanyProfileController::class)->only(['show', 'store']);
    Route::apiResource('/business-category', BusinessCategoryController::class)->only(['index']);
    Route::get('/business-category/{id}/subcategories', [BusinessCategoryController::class, 'getSubCategoriesByBusinessCategory']);
});
