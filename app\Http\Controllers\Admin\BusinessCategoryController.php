<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ImageUploadHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\BusinessCategoryRequest;
use App\Http\Resources\BusinessCategoryResource;
use App\Models\BusinessCategory;

class BusinessCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $business_categories = BusinessCategory::with('subCategories')->get();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Business categories fetched successfully',
            'data' => BusinessCategoryResource::collection($business_categories)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BusinessCategoryRequest $request)
    {
        try {
            $validatedData = $request->validated();

            $validatedData['slug'] = str_replace(' ', '-', strtolower($validatedData['name']));

            if ($request->hasFile('image')) {
                $imageFile = $request->file('image');
                $imageName = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
                $imagePath = (new ImageUploadHelper())->uploadImage($imageFile, 'public/uploads/business-category-images', $imageName);

                $validatedData['image'] = $imagePath;
            }

            $business_category = BusinessCategory::create($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Business category created successfully',
                'data' => new BusinessCategoryResource($business_category)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to create business category'
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // $business_category = BusinessCategory::find($id);
        $business_category = BusinessCategory::with('subcategories')->find($id);

        if (!$business_category) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'Business category not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Business category fetched successfully',
            'data' => new BusinessCategoryResource($business_category)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BusinessCategoryRequest $request, string $id)
    {
        try {
            $validatedData = $request->validated();

            $validatedData['slug'] = str_replace(' ', '-', strtolower($validatedData['name']));

            $business_category = BusinessCategory::find($id);

            if (!$business_category) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Business category not found'
                ], 404);
            }

            if ($request->hasFile('image')) {
                $imageFile = $request->file('image');
                $imageName = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
                $imagePath = (new ImageUploadHelper())->uploadImage($imageFile, 'public/uploads/business-category-images', $imageName);

                $validatedData['image'] = $imagePath;
            }

            $business_category->update($validatedData);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Business category updated successfully',
                'data' => new BusinessCategoryResource($business_category)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to update business category'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $business_category = BusinessCategory::with('subcategories')->find($id);

            if (!$business_category) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'Business category not found'
                ], 404);
            }

            $business_category->delete();

            if ($business_category->subcategories->count() > 0) {
                foreach ($business_category->subcategories as $subCategory) {
                    $subCategory->delete();
                }
            }

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Business category deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Failed to delete business category'
            ], 500);
        }
    }

    public function getSubCategoriesByBusinessCategory(string $id)
    {
        $business_category = BusinessCategory::with('subcategories')->find($id);

        if (!$business_category) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'Business category not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Subcategories fetched successfully',
            'data' => BusinessCategoryResource::collection($business_category->subcategories)
        ]);
    }
}
