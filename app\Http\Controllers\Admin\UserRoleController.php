<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Admin;
use App\Models\Vendor;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserRoleController extends Controller
{
    /**
     * Get user roles for a specific user type and ID.
     */
    public function getUserRoles(Request $request, $userType, $userId)
    {
        try {
            $user = $this->getUserByTypeAndId($userType, $userId);
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'User not found'
                ], 404);
            }

            $roles = $user->roles()->get();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'User roles retrieved successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'type' => $userType
                    ],
                    'roles' => $roles
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Assign roles to a user.
     */
    public function assignRoles(Request $request, $userType, $userId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'roles' => 'required|array',
                'roles.*' => 'exists:roles,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = $this->getUserByTypeAndId($userType, $userId);
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'User not found'
                ], 404);
            }

            // Sync roles (this will replace existing roles with new ones)
            $user->roles()->sync($request->roles);

            $user->load('roles');

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Roles assigned successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'type' => $userType
                    ],
                    'roles' => $user->roles
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Remove specific roles from a user.
     */
    public function removeRoles(Request $request, $userType, $userId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'roles' => 'required|array',
                'roles.*' => 'exists:roles,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = $this->getUserByTypeAndId($userType, $userId);
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'status_code' => 404,
                    'message' => 'User not found'
                ], 404);
            }

            // Detach specific roles
            $user->roles()->detach($request->roles);

            $user->load('roles');

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Roles removed successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'type' => $userType
                    ],
                    'roles' => $user->roles
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Get user by type and ID.
     */
    private function getUserByTypeAndId($userType, $userId)
    {
        switch (strtolower($userType)) {
            case 'admin':
                return Admin::find($userId);
            case 'customer':
            case 'user':
                return User::find($userId);
            case 'vendor':
                return Vendor::find($userId);
            default:
                return null;
        }
    }

    /**
     * Get all users with their roles for a specific user type.
     */
    public function getUsersByType(Request $request, $userType)
    {
        try {
            $query = null;
            
            switch (strtolower($userType)) {
                case 'admin':
                    $query = Admin::with('roles');
                    break;
                case 'customer':
                case 'user':
                    $query = User::with('roles');
                    break;
                case 'vendor':
                    $query = Vendor::with('roles');
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'status_code' => 400,
                        'message' => 'Invalid user type'
                    ], 400);
            }

            $users = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Users retrieved successfully',
                'data' => $users
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }
}
