# Roles and Permissions System

This document explains how to use the roles and permissions system implemented in the ZetaBid Backend application.

## Overview

The system provides a flexible role-based access control (RBAC) that works across all three user types:
- **Admin** - Administrative users
- **Customer/User** - Regular customers
- **Vendor** - Vendor users

## Database Structure

### Tables
- `roles` - Stores role definitions
- `permissions` - Stores permission definitions
- `role_permissions` - Many-to-many relationship between roles and permissions
- `user_roles` - Polymorphic many-to-many relationship between users and roles

### Models
- `Role` - Role model with relationships to permissions and users
- `Permission` - Permission model with relationships to roles
- `HasRolesAndPermissions` trait - Added to User, Admin, and Vendor models

## Default Roles

The system comes with these default roles:

1. **super_admin** - Has access to all system features and permissions
2. **admin** - Has access to most administrative features
3. **manager** - Has access to management features
4. **customer** - Regular customer with basic access
5. **vendor** - Vendor with access to vendor features
6. **moderator** - Can moderate content and users

## Default Permissions

Permissions are organized by groups:

### Admin Management
- `admin.view`, `admin.create`, `admin.edit`, `admin.delete`

### User Management
- `user.view`, `user.create`, `user.edit`, `user.delete`

### Vendor Management
- `vendor.view`, `vendor.create`, `vendor.edit`, `vendor.delete`

### Role Management
- `role.view`, `role.create`, `role.edit`, `role.delete`

### Permission Management
- `permission.view`, `permission.create`, `permission.edit`, `permission.delete`

### Business Category Management
- `business_category.view`, `business_category.create`, `business_category.edit`, `business_category.delete`

### Training Session Management
- `training_session.view`, `training_session.create`, `training_session.edit`, `training_session.delete`

### FAQ Management
- `faq.view`, `faq.create`, `faq.edit`, `faq.delete`

### Notice Management
- `notice.view`, `notice.create`, `notice.edit`, `notice.delete`

### Page Management
- `page.view`, `page.edit`

### Dashboard Access
- `dashboard.admin`, `dashboard.customer`, `dashboard.vendor`

## API Endpoints

### Role Management (Super Admin only)
```
GET    /api/admin/roles              - List all roles
POST   /api/admin/roles              - Create a new role
GET    /api/admin/roles/{id}         - Get specific role
PUT    /api/admin/roles/{id}         - Update role
DELETE /api/admin/roles/{id}         - Delete role
```

### Permission Management (Super Admin only)
```
GET    /api/admin/permissions        - List all permissions
POST   /api/admin/permissions        - Create a new permission
GET    /api/admin/permissions/{id}   - Get specific permission
PUT    /api/admin/permissions/{id}   - Update permission
DELETE /api/admin/permissions/{id}   - Delete permission
GET    /api/admin/permissions/grouped - Get permissions grouped by category
```

### User Role Management (Super Admin only)
```
GET    /api/admin/users/{userType}                    - List users by type (admin/customer/vendor)
GET    /api/admin/users/{userType}/{userId}/roles     - Get user's roles
POST   /api/admin/users/{userType}/{userId}/roles     - Assign roles to user
DELETE /api/admin/users/{userType}/{userId}/roles     - Remove roles from user
```

## Usage Examples

### Using Middleware in Routes

```php
// Require specific role
Route::middleware(['role:super_admin'])->group(function () {
    // Routes only accessible by super admins
});

// Require specific permission
Route::middleware(['permission:user.create'])->post('/users', [UserController::class, 'store']);

// Require multiple permissions (user needs ANY of them)
Route::middleware(['permission:user.edit,user.view'])->get('/users/{id}', [UserController::class, 'show']);
```

### Using in Controllers

```php
// Check if user has role
if ($user->hasRole('admin')) {
    // User is an admin
}

// Check if user has permission
if ($user->hasPermission('user.create')) {
    // User can create users
}

// Check multiple roles
if ($user->hasAnyRole(['admin', 'manager'])) {
    // User is either admin or manager
}

// Check multiple permissions
if ($user->hasAllPermissions(['user.view', 'user.edit'])) {
    // User has both permissions
}
```

### Assigning Roles

```php
// Assign role to user
$user->assignRole('admin');

// Assign multiple roles
$user->syncRoles(['admin', 'manager']);

// Remove role
$user->removeRole('admin');
```

### Working with Roles and Permissions

```php
// Create a new role
$role = Role::create([
    'name' => 'editor',
    'display_name' => 'Content Editor',
    'description' => 'Can edit content'
]);

// Assign permissions to role
$role->assignPermission('article.edit');

// Create permission
$permission = Permission::create([
    'name' => 'article.publish',
    'display_name' => 'Publish Articles',
    'group' => 'article'
]);
```

## Setup Instructions

1. **Run Migrations**
   ```bash
   php artisan migrate
   ```

2. **Run Seeders**
   ```bash
   php artisan db:seed --class=RolesAndPermissionsSeeder
   ```

3. **Assign Default Roles**
   The seeder automatically assigns:
   - `super_admin` role to the first admin
   - `customer` role to all existing customers
   - `vendor` role to all existing vendors

## Security Notes

- Always use middleware to protect routes
- Super admin role has access to all permissions
- Permissions are checked through roles, not directly assigned to users
- The system uses polymorphic relationships to support multiple user types
- All role and permission management is restricted to super admins

## Testing

To test the system:

1. Create a user and assign roles
2. Test API endpoints with different user roles
3. Verify middleware is working correctly
4. Check that permissions are properly enforced

## Extending the System

To add new permissions:

1. Add them to the `RolesAndPermissionsSeeder`
2. Assign them to appropriate roles
3. Use them in middleware or controller checks
4. Update this documentation

To add new roles:

1. Define them in the seeder
2. Assign appropriate permissions
3. Update role assignment logic if needed
