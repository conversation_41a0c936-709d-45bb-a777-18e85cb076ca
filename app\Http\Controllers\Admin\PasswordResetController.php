<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Admin;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use App\Mail\PasswordResetMail;

class PasswordResetController extends Controller
{

    public function createPasswordResetRequest(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email|exists:admins,email',
            ]);

            $code = mt_rand(1000, 9999);

            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $request->email],
                [
                    'token' => $code,
                    'created_at' => Carbon::now()
                ]
            );

            Mail::to($request->email)->send(new PasswordResetMail($code));

            return response()->json(
                [
                    'status' => true,
                    'status_code' => 200,
                    'message' => 'Password reset token sent via email.'
                ]
            );
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'status_code' => 500,
                'message' => 'Failed to send password reset token. Please try again later.'
            ], 500);
        }
    }


    public function resetPassword(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email|exists:admins,email',
                'token' => 'required',
                'password' => 'required|min:8|confirmed',
            ]);

            $passwordResetEntry = DB::table('password_reset_tokens')
                ->where('email', $request->email)
                ->first();

            $tokenLifetime = 30;

            if (!$passwordResetEntry || Carbon::parse($passwordResetEntry->created_at)->addMinutes($tokenLifetime)->isPast()) {
                return response()->json([
                    'status' => false,
                    'status_code' => 401,
                    'message' => 'Invalid or expired token.'
                ], 401);
            }

            if ($request->token != $passwordResetEntry->token) {
                return response()->json([
                    'status' => false,
                    'status_code' => 401,
                    'message' => 'Invalid token.'
                ], 401);
            }

            $admin = Admin::where('email', $request->email)->first();
            $admin->password = Hash::make($request->password);
            $admin->save();


            DB::table('password_reset_tokens')->where('email', $request->email)->delete();

            return response()->json([
                'status' => true,
                'status_code' => 200,
                'message' => 'Password has been successfully reset.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'status_code' => 500,
                'message' => 'Failed to reset password. Please try again later.'
            ], 500);
        }
    }
}
