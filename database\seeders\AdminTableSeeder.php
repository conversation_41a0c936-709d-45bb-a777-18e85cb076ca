<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AdminTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('admins')->insert([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'profile' => 'profile.png',
            'password' => Hash::make('admin@123'),
            'status' => true,
        ]);
    }
}
