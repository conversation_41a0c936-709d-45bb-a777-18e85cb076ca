<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Admin;
use App\Models\User;
use App\Models\Vendor;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Permissions
        $permissions = [
            // Admin Management
            ['name' => 'admin.view', 'display_name' => 'View Admins', 'group' => 'admin'],
            ['name' => 'admin.create', 'display_name' => 'Create Admins', 'group' => 'admin'],
            ['name' => 'admin.edit', 'display_name' => 'Edit Admins', 'group' => 'admin'],
            ['name' => 'admin.delete', 'display_name' => 'Delete Admins', 'group' => 'admin'],

            // User Management
            ['name' => 'user.view', 'display_name' => 'View Users', 'group' => 'user'],
            ['name' => 'user.create', 'display_name' => 'Create Users', 'group' => 'user'],
            ['name' => 'user.edit', 'display_name' => 'Edit Users', 'group' => 'user'],
            ['name' => 'user.delete', 'display_name' => 'Delete Users', 'group' => 'user'],

            // Vendor Management
            ['name' => 'vendor.view', 'display_name' => 'View Vendors', 'group' => 'vendor'],
            ['name' => 'vendor.create', 'display_name' => 'Create Vendors', 'group' => 'vendor'],
            ['name' => 'vendor.edit', 'display_name' => 'Edit Vendors', 'group' => 'vendor'],
            ['name' => 'vendor.delete', 'display_name' => 'Delete Vendors', 'group' => 'vendor'],

            // Role Management
            ['name' => 'role.view', 'display_name' => 'View Roles', 'group' => 'role'],
            ['name' => 'role.create', 'display_name' => 'Create Roles', 'group' => 'role'],
            ['name' => 'role.edit', 'display_name' => 'Edit Roles', 'group' => 'role'],
            ['name' => 'role.delete', 'display_name' => 'Delete Roles', 'group' => 'role'],

            // Permission Management
            ['name' => 'permission.view', 'display_name' => 'View Permissions', 'group' => 'permission'],
            ['name' => 'permission.create', 'display_name' => 'Create Permissions', 'group' => 'permission'],
            ['name' => 'permission.edit', 'display_name' => 'Edit Permissions', 'group' => 'permission'],
            ['name' => 'permission.delete', 'display_name' => 'Delete Permissions', 'group' => 'permission'],

            // Business Category Management
            ['name' => 'business_category.view', 'display_name' => 'View Business Categories', 'group' => 'business_category'],
            ['name' => 'business_category.create', 'display_name' => 'Create Business Categories', 'group' => 'business_category'],
            ['name' => 'business_category.edit', 'display_name' => 'Edit Business Categories', 'group' => 'business_category'],
            ['name' => 'business_category.delete', 'display_name' => 'Delete Business Categories', 'group' => 'business_category'],

            // Training Session Management
            ['name' => 'training_session.view', 'display_name' => 'View Training Sessions', 'group' => 'training_session'],
            ['name' => 'training_session.create', 'display_name' => 'Create Training Sessions', 'group' => 'training_session'],
            ['name' => 'training_session.edit', 'display_name' => 'Edit Training Sessions', 'group' => 'training_session'],
            ['name' => 'training_session.delete', 'display_name' => 'Delete Training Sessions', 'group' => 'training_session'],

            // FAQ Management
            ['name' => 'faq.view', 'display_name' => 'View FAQs', 'group' => 'faq'],
            ['name' => 'faq.create', 'display_name' => 'Create FAQs', 'group' => 'faq'],
            ['name' => 'faq.edit', 'display_name' => 'Edit FAQs', 'group' => 'faq'],
            ['name' => 'faq.delete', 'display_name' => 'Delete FAQs', 'group' => 'faq'],

            // Notice Management
            ['name' => 'notice.view', 'display_name' => 'View Notices', 'group' => 'notice'],
            ['name' => 'notice.create', 'display_name' => 'Create Notices', 'group' => 'notice'],
            ['name' => 'notice.edit', 'display_name' => 'Edit Notices', 'group' => 'notice'],
            ['name' => 'notice.delete', 'display_name' => 'Delete Notices', 'group' => 'notice'],

            // Page Management
            ['name' => 'page.view', 'display_name' => 'View Pages', 'group' => 'page'],
            ['name' => 'page.edit', 'display_name' => 'Edit Pages', 'group' => 'page'],

            // Dashboard Access
            ['name' => 'dashboard.admin', 'display_name' => 'Admin Dashboard Access', 'group' => 'dashboard'],
            ['name' => 'dashboard.customer', 'display_name' => 'Customer Dashboard Access', 'group' => 'dashboard'],
            ['name' => 'dashboard.vendor', 'display_name' => 'Vendor Dashboard Access', 'group' => 'dashboard'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // Create Roles
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'Has access to all system features and permissions'
            ],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Has access to most administrative features'
            ],
            [
                'name' => 'manager',
                'display_name' => 'Manager',
                'description' => 'Has access to management features'
            ],
            [
                'name' => 'customer',
                'display_name' => 'Customer',
                'description' => 'Regular customer with basic access'
            ],
            [
                'name' => 'vendor',
                'display_name' => 'Vendor',
                'description' => 'Vendor with access to vendor features'
            ],
            [
                'name' => 'moderator',
                'display_name' => 'Moderator',
                'description' => 'Can moderate content and users'
            ]
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );

            // Assign permissions to roles
            if ($roleData['name'] === 'super_admin') {
                // Super admin gets all permissions
                $role->permissions()->sync(Permission::all()->pluck('id'));
            } elseif ($roleData['name'] === 'admin') {
                // Admin gets most permissions except super admin specific ones
                $adminPermissions = Permission::whereNotIn('name', [
                    'admin.delete', 'role.delete', 'permission.delete'
                ])->pluck('id');
                $role->permissions()->sync($adminPermissions);
            } elseif ($roleData['name'] === 'manager') {
                // Manager gets view and edit permissions for most resources
                $managerPermissions = Permission::where(function ($query) {
                    $query->where('name', 'like', '%.view')
                          ->orWhere('name', 'like', '%.edit')
                          ->orWhere('name', 'like', '%.create');
                })->whereNotIn('group', ['admin', 'role', 'permission'])->pluck('id');
                $role->permissions()->sync($managerPermissions);
            } elseif ($roleData['name'] === 'customer') {
                // Customer gets basic dashboard access
                $customerPermissions = Permission::whereIn('name', [
                    'dashboard.customer'
                ])->pluck('id');
                $role->permissions()->sync($customerPermissions);
            } elseif ($roleData['name'] === 'vendor') {
                // Vendor gets vendor-related permissions
                $vendorPermissions = Permission::whereIn('name', [
                    'dashboard.vendor',
                    'business_category.view'
                ])->pluck('id');
                $role->permissions()->sync($vendorPermissions);
            }
        }

        // Assign default roles to existing users
        $this->assignDefaultRoles();
    }

    /**
     * Assign default roles to existing users.
     */
    private function assignDefaultRoles()
    {
        // Assign super_admin role to the first admin
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $firstAdmin = Admin::first();
        if ($firstAdmin && $superAdminRole) {
            $firstAdmin->assignRole($superAdminRole);
        }

        // Assign customer role to all existing customers
        $customerRole = Role::where('name', 'customer')->first();
        if ($customerRole) {
            $customers = User::all();
            foreach ($customers as $customer) {
                $customer->assignRole($customerRole);
            }
        }

        // Assign vendor role to all existing vendors
        $vendorRole = Role::where('name', 'vendor')->first();
        if ($vendorRole) {
            $vendors = Vendor::all();
            foreach ($vendors as $vendor) {
                $vendor->assignRole($vendorRole);
            }
        }
    }
}
