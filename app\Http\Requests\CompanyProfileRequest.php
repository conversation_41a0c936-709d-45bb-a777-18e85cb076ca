<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompanyProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'logo' => 'nullable|file|mimes:jpg,jpeg,png,gif|max:2048',
            'email' => 'required|email|max:255',
            'company_representative' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'province' => 'required|string|max:255',
            'district' => 'required|string|max:255',
            'municipality' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'business_type' => 'required|string|max:255',
            'website' => 'nullable|url|max:255',
            'company_registration' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'vat_pan' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'tax_clearance' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'additional_information' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'ward_no' => 'nullable|string|max:255',
            'street_address' => 'nullable|string|max:255',
            'tole' => 'nullable|string|max:255',
            'bank_account_number' => 'nullable|string|max:255',
            'account_holder_name' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'bank_branch' => 'nullable|string|max:255',
            'audit_report' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            'signature' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
        ];
    }
}
