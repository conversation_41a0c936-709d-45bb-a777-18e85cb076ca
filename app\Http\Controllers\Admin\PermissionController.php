<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PermissionController extends Controller
{
    /**
     * Display a listing of permissions.
     */
    public function index(Request $request)
    {
        try {
            $query = Permission::query();

            // Filter by active status if provided
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Filter by group if provided
            if ($request->has('group')) {
                $query->where('group', $request->get('group'));
            }

            // Search by name or display_name
            if ($request->has('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('display_name', 'like', "%{$search}%");
                });
            }

            $permissions = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Permissions retrieved successfully',
                'data' => $permissions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Store a newly created permission.
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:permissions,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'group' => 'nullable|string|max:255',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'errors' => $validator->errors()
                ], 422);
            }

            $permission = Permission::create($validator->validated());

            return response()->json([
                'success' => true,
                'status_code' => 201,
                'message' => 'Permission created successfully',
                'data' => $permission
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Display the specified permission.
     */
    public function show($id)
    {
        try {
            $permission = Permission::findOrFail($id);

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Permission retrieved successfully',
                'data' => $permission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 404,
                'message' => 'Permission not found'
            ], 404);
        }
    }

    /**
     * Update the specified permission.
     */
    public function update(Request $request, $id)
    {
        try {
            $permission = Permission::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:permissions,name,' . $id,
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'group' => 'nullable|string|max:255',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'errors' => $validator->errors()
                ], 422);
            }

            $permission->update($validator->validated());

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Permission updated successfully',
                'data' => $permission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Remove the specified permission.
     */
    public function destroy($id)
    {
        try {
            $permission = Permission::findOrFail($id);
            $permission->delete();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Permission deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }

    /**
     * Get permissions grouped by their group.
     */
    public function getGrouped()
    {
        try {
            $permissions = Permission::active()->get()->groupBy('group');

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Grouped permissions retrieved successfully',
                'data' => $permissions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'Something went wrong, please try again'
            ], 500);
        }
    }
}
