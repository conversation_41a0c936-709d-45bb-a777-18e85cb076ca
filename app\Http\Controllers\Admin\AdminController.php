<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\ImageUploadHelper;
use App\Http\Resources\AdminResource;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public function profile(Request $request)
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'status_code' => 401,
                'message' => 'Unauthorized'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Profile fetched successfully',
            'data' => new AdminResource($request->user())
        ]);
    }
    public function logout(Request $request)
    {
        // Check if the user is authenticated
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'status_code' => 401,
                'message' => 'Unauthorized'
            ], 401);
        }

        // Revoke all tokens for the authenticated user
        $request->user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Logged out successfully'
        ]);
    }

    // Token Refresh
    public function refresh(Request $request)
    {
        $token = $request->user()->createToken('admin-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Token refreshed successfully',
            'token' => $token
        ]);
    }

    // Change Password
    public function changePassword(Request $request)
    {
        try {
            $user = $request->user();

            $validatedData = $request->validate([
                'current_password' => 'required',
                'password' => 'required|string|min:8|confirmed',
            ]);

            if (!Hash::check($validatedData['current_password'], $user->password)) {
                return response()->json([
                    'success' => false,
                    'status_code' => 422,
                    'message' => 'Current password is incorrect'
                ], 422);
            }

            $user->password = Hash::make($validatedData['password']);
            $user->save();

            return response()->json([
                'success' => true,
                'status_code' => 200,
                'message' => 'Password changed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status_code' => 500,
                'message' => 'An error occurred while changing password'
            ], 500);
        }
    }

    public function resetUserPassword(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'password' => 'required|min:8|confirmed',
            ]);

            // Find the user by ID
            $user = User::find($request->user_id);

            if (!$user) {
                return response()->json([
                    'status' => false,
                    'status_code' => 404,
                    'message' => 'User not found'
                ], 404);
            }

            // Update user's password
            $user->password = Hash::make($request->password);
            $user->save();

            return response()->json([
                'status' => true,
                'status_code' => 200,
                'message' => 'Password reset successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'status_code' => 500,
                'message' => 'An error occurred while resetting password'
            ], 500);
        }
    }

    public function updateProfile(Request $request)
    {
        $admin = Auth::user();

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:admins,email,' . $admin->id,
            'address' => 'nullable|string',
            'profile' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:5120',
        ]);

        $admin->fill($validatedData);
        // Validate and handle profile photo upload
        if ($request->hasFile('profile')) {
            $profilePhotoFile = $request->file('profile');
            $profilePhotoName = pathinfo($profilePhotoFile->getClientOriginalName(), PATHINFO_FILENAME);
            $profilePhotoPath = (new ImageUploadHelper())->uploadImage($profilePhotoFile, 'public/uploads/profile-photos', $profilePhotoName);

            $admin->profile = $profilePhotoPath;
        }
        $admin->save();

        return response()->json([
            'success' => true,
            'status_code' => 200,
            'message' => 'Profile updated successfully',
            'data' => new AdminResource($admin)
        ], 200);
    }
}
