<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class UserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->insert([
            'name' => 'Demo Customer',
            'email' => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'profile' => 'profile.png',
            'password' => Hash::make('customer@123'),
            'status' => true,
            'kyc_status' => 'pending'
        ]);
    }
}
